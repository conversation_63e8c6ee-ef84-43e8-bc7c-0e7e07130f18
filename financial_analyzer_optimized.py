import yfinance as yf
from datetime import datetime, timedelta
from textblob import TextBlob
import numpy as np
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from functools import lru_cache
import pytz
import feedparser
import requests
import xml.etree.ElementTree as ET
from urllib.parse import urljoin
import re

# Constants
MAJOR_TICKERS = [
    # Tech giants
    'AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA', 'META', 'NVDA', 'NFLX', 'ADBE', 'CRM', 'ORCL', 'INTC', 'AMD',
    # Financial
    'JPM', 'BAC', 'WFC', 'GS', 'MS', 'C', 'BRK-B', 'V', 'MA', 'AXP', 'COF', 'USB', 'PNC', 'TFC',
    # Healthcare
    'JNJ', 'UNH', 'PFE', 'ABT', 'TMO', 'DHR', 'BMY', 'AMGN', 'GILD', 'BIIB', 'REGN', 'VRTX',
    # Consumer
    'WMT', 'HD', 'PG', 'KO', 'PEP', 'MCD', 'NKE', 'SBUX', 'TGT', 'COST', 'LOW', 'DIS',
    # Industrial
    'BA', 'CAT', 'GE', 'MMM', 'HON', 'UPS', 'FDX', 'LMT', 'RTX', 'NOC', 'GD',
    # Energy
    'XOM', 'CVX', 'COP', 'EOG', 'SLB', 'MPC', 'VLO', 'PSX', 'KMI', 'OKE',
    # ETFs and Indices
    'SPY', 'QQQ', 'IWM', 'DIA', 'VTI', 'VOO', 'VEA', 'VWO', 'GLD', 'SLV'
]

MARKET_INDICES = {
    'SPY': 'S&P 500',
    'QQQ': 'NASDAQ',
    'DIA': 'Dow Jones',
    'IWM': 'Russell 2000',
    'VTI': 'Total Stock Market'
}

# Government RSS Feeds for Policy News
GOVERNMENT_RSS_FEEDS = {
    'fed_press': {
        'url': 'https://www.federalreserve.gov/feeds/press_all.xml',
        'name': 'Federal Reserve Press Releases',
        'category': 'monetary_policy',
        'impact_weight': 0.9  # High impact on markets
    },
    'fed_monetary': {
        'url': 'https://www.federalreserve.gov/feeds/press_monetary.xml',
        'name': 'Fed Monetary Policy',
        'category': 'monetary_policy',
        'impact_weight': 1.0  # Highest impact
    },
    'fed_speeches': {
        'url': 'https://www.federalreserve.gov/feeds/speeches.xml',
        'name': 'Fed Speeches & Testimony',
        'category': 'monetary_policy',
        'impact_weight': 0.7
    },
    'fed_banking_reg': {
        'url': 'https://www.federalreserve.gov/feeds/press_bcreg.xml',
        'name': 'Fed Banking Regulation',
        'category': 'regulatory',
        'impact_weight': 0.6
    },
    'fed_enforcement': {
        'url': 'https://www.federalreserve.gov/feeds/press_enforcement.xml',
        'name': 'Fed Enforcement Actions',
        'category': 'regulatory',
        'impact_weight': 0.5
    }
}

# Policy keywords that indicate market impact
POLICY_KEYWORDS = {
    'high_impact': [
        'interest rate', 'federal funds rate', 'monetary policy', 'quantitative easing',
        'inflation target', 'recession', 'economic outlook', 'gdp growth',
        'unemployment rate', 'fomc', 'rate hike', 'rate cut', 'dovish', 'hawkish'
    ],
    'medium_impact': [
        'banking regulation', 'stress test', 'capital requirements', 'liquidity',
        'financial stability', 'systemic risk', 'basel', 'dodd-frank',
        'consumer protection', 'enforcement action'
    ],
    'sector_specific': [
        'energy policy', 'healthcare reform', 'tax policy', 'trade policy',
        'infrastructure', 'climate policy', 'technology regulation',
        'antitrust', 'merger', 'acquisition'
    ]
}

@lru_cache(maxsize=128)
def get_ticker_sector(ticker):
    """Map tickers to their sectors/industries - cached for performance"""
    sector_mapping = {
        # Technology
        'AAPL': 'Technology', 'MSFT': 'Technology', 'GOOGL': 'Technology', 'AMZN': 'Technology',
        'META': 'Technology', 'NVDA': 'Technology', 'NFLX': 'Technology', 'ADBE': 'Technology',
        'CRM': 'Technology', 'ORCL': 'Technology', 'INTC': 'Technology', 'AMD': 'Technology',
        'TSLA': 'Technology',
        # Financial Services
        'JPM': 'Financial', 'BAC': 'Financial', 'WFC': 'Financial', 'GS': 'Financial',
        'MS': 'Financial', 'C': 'Financial', 'BRK-B': 'Financial', 'V': 'Financial',
        'MA': 'Financial', 'AXP': 'Financial', 'COF': 'Financial', 'USB': 'Financial',
        'PNC': 'Financial', 'TFC': 'Financial',
        # Healthcare & Pharmaceuticals
        'JNJ': 'Healthcare', 'UNH': 'Healthcare', 'PFE': 'Healthcare', 'ABT': 'Healthcare',
        'TMO': 'Healthcare', 'DHR': 'Healthcare', 'BMY': 'Healthcare', 'AMGN': 'Healthcare',
        'GILD': 'Healthcare', 'BIIB': 'Healthcare', 'REGN': 'Healthcare', 'VRTX': 'Healthcare',
        # Consumer Goods & Retail
        'WMT': 'Consumer', 'HD': 'Consumer', 'PG': 'Consumer', 'KO': 'Consumer',
        'PEP': 'Consumer', 'MCD': 'Consumer', 'NKE': 'Consumer', 'SBUX': 'Consumer',
        'TGT': 'Consumer', 'COST': 'Consumer', 'LOW': 'Consumer', 'DIS': 'Consumer',
        # Industrial
        'BA': 'Industrial', 'CAT': 'Industrial', 'GE': 'Industrial', 'MMM': 'Industrial',
        'HON': 'Industrial', 'UPS': 'Industrial', 'FDX': 'Industrial', 'LMT': 'Industrial',
        'RTX': 'Industrial', 'NOC': 'Industrial', 'GD': 'Industrial',
        # Energy
        'XOM': 'Energy', 'CVX': 'Energy', 'COP': 'Energy', 'EOG': 'Energy',
        'SLB': 'Energy', 'MPC': 'Energy', 'VLO': 'Energy', 'PSX': 'Energy',
        'KMI': 'Energy', 'OKE': 'Energy',
        # ETFs & Index Funds
        'SPY': 'ETF', 'QQQ': 'ETF', 'IWM': 'ETF', 'DIA': 'ETF',
        'VTI': 'ETF', 'VOO': 'ETF', 'VEA': 'ETF', 'VWO': 'ETF',
        'GLD': 'Commodities', 'SLV': 'Commodities'
    }
    return sector_mapping.get(ticker, 'Other')

def create_hyperlink(url, text):
    """Create a clickable hyperlink for terminal output"""
    if url and url.strip():
        # ANSI escape sequence for hyperlinks: \033]8;;URL\033\\TEXT\033]8;;\033\\
        return f"\033]8;;{url}\033\\{text}\033]8;;\033\\"
    else:
        return text

def get_time_ago(dt_obj):
    """Calculate how long ago an article was published"""
    # Convert to CDT timezone
    cdt = pytz.timezone('America/Chicago')
    now = datetime.now(cdt)

    # Convert article time to CDT if it has timezone info
    if dt_obj.tzinfo:
        dt_cdt = dt_obj.astimezone(cdt)
    else:
        # Assume UTC if no timezone info
        utc = pytz.UTC
        dt_utc = utc.localize(dt_obj)
        dt_cdt = dt_utc.astimezone(cdt)

    diff = now - dt_cdt

    if diff.days > 0:
        return f"{diff.days} day{'s' if diff.days > 1 else ''} ago"
    elif diff.seconds > 3600:
        hours = diff.seconds // 3600
        return f"{hours} hour{'s' if hours > 1 else ''} ago"
    elif diff.seconds > 60:
        minutes = diff.seconds // 60
        return f"{minutes} minute{'s' if minutes > 1 else ''} ago"
    else:
        return "Just now"

def get_ticker_price_change(ticker, days=1):
    """Get price change for a ticker over specified days"""
    try:
        stock = yf.Ticker(ticker)
        # Get recent data (last 5 days to ensure we have enough data)
        hist = stock.history(period="5d")

        if len(hist) >= 2:
            # Calculate change from days ago to most recent close
            if len(hist) >= days + 1:
                old_price = hist['Close'].iloc[-(days+1)]
                new_price = hist['Close'].iloc[-1]
            else:
                # Fallback to available data
                old_price = hist['Close'].iloc[0]
                new_price = hist['Close'].iloc[-1]

            price_change = ((new_price / old_price) - 1) * 100
            return float(price_change)
        else:
            return 0.0
    except Exception:
        return 0.0

def get_multiple_ticker_prices(tickers, days=1):
    """Get price changes for multiple tickers efficiently"""
    price_changes = {}

    # Use parallel processing for price fetching
    with ThreadPoolExecutor(max_workers=5) as executor:
        future_to_ticker = {executor.submit(get_ticker_price_change, ticker, days): ticker for ticker in tickers}

        for future in as_completed(future_to_ticker):
            ticker = future_to_ticker[future]
            try:
                price_change = future.result()
                price_changes[ticker] = price_change
            except Exception:
                price_changes[ticker] = 0.0

    return price_changes

def get_analyst_recommendation(ticker):
    """Get analyst recommendation from yfinance"""
    try:
        stock = yf.Ticker(ticker)
        info = stock.info

        # Get recommendation data
        recommendation = info.get('recommendationKey', 'N/A')
        recommendation_mean = info.get('recommendationMean', None)
        target_price = info.get('targetMeanPrice', None)
        current_price = info.get('currentPrice', None) or info.get('regularMarketPrice', None)

        # Convert recommendation key to readable format
        rec_mapping = {
            'strong_buy': 'Strong Buy',
            'buy': 'Buy',
            'hold': 'Hold',
            'sell': 'Sell',
            'strong_sell': 'Strong Sell'
        }

        readable_rec = rec_mapping.get(recommendation, recommendation)

        # Calculate upside if we have target and current price
        upside = None
        if target_price and current_price and current_price > 0:
            upside = ((target_price / current_price) - 1) * 100

        return {
            'recommendation': readable_rec,
            'recommendation_mean': recommendation_mean,
            'target_price': target_price,
            'current_price': current_price,
            'upside_potential': upside
        }
    except Exception:
        return {
            'recommendation': 'N/A',
            'recommendation_mean': None,
            'target_price': None,
            'current_price': None,
            'upside_potential': None
        }

def get_multiple_analyst_recommendations(tickers):
    """Get analyst recommendations for multiple tickers efficiently"""
    recommendations = {}

    # Use parallel processing for recommendation fetching
    with ThreadPoolExecutor(max_workers=5) as executor:
        future_to_ticker = {executor.submit(get_analyst_recommendation, ticker): ticker for ticker in tickers}

        for future in as_completed(future_to_ticker):
            ticker = future_to_ticker[future]
            try:
                rec_data = future.result()
                recommendations[ticker] = rec_data
            except Exception:
                recommendations[ticker] = {
                    'recommendation': 'N/A',
                    'recommendation_mean': None,
                    'target_price': None,
                    'current_price': None,
                    'upside_potential': None
                }

    return recommendations

def fetch_news_for_ticker(ticker):
    """Fetch news for a single ticker - optimized for parallel processing"""
    try:
        stock = yf.Ticker(ticker)
        news = stock.news
        
        if not news:
            return []
            
        articles = []
        for article in news[:3]:  # Top 3 articles per ticker
            content = article.get('content', article)
            headline = content.get('title', '')
            summary = content.get('summary', '') or content.get('description', '')
            pub_date = content.get('pubDate', '')
            
            if headline and len(headline) > 10:
                # Enhanced date/time parsing with CDT timezone
                try:
                    if pub_date:
                        dt_obj = datetime.fromisoformat(pub_date.replace('Z', '+00:00'))

                        # Convert to CDT
                        cdt = pytz.timezone('America/Chicago')
                        if dt_obj.tzinfo:
                            dt_cdt = dt_obj.astimezone(cdt)
                        else:
                            utc = pytz.UTC
                            dt_utc = utc.localize(dt_obj)
                            dt_cdt = dt_utc.astimezone(cdt)

                        formatted_date = dt_cdt.strftime('%Y-%m-%d')
                        formatted_datetime = dt_cdt.strftime('%Y-%m-%d %H:%M:%S CDT')
                        time_ago = get_time_ago(dt_obj)
                    else:
                        cdt = pytz.timezone('America/Chicago')
                        now = datetime.now(cdt)
                        formatted_date = now.strftime('%Y-%m-%d')
                        formatted_datetime = now.strftime('%Y-%m-%d %H:%M:%S CDT')
                        time_ago = "Just now"
                except:
                    cdt = pytz.timezone('America/Chicago')
                    now = datetime.now(cdt)
                    formatted_date = now.strftime('%Y-%m-%d')
                    formatted_datetime = now.strftime('%Y-%m-%d %H:%M:%S CDT')
                    time_ago = "Unknown"

                articles.append({
                    'headline': headline,
                    'text': summary or headline,
                    'date': formatted_date,
                    'datetime': formatted_datetime,
                    'time_ago': time_ago,
                    'source': f'Yahoo Finance ({ticker})',
                    'ticker': ticker,
                    'url': content.get('canonicalUrl', {}).get('url', '') or content.get('clickThroughUrl', {}).get('url', '')
                })
        
        return articles
    except Exception:
        return []

def fetch_market_news_parallel():
    """Fetch news using parallel processing for better performance"""
    print("Fetching news from major stock tickers using parallel processing...")
    print(f"Processing {len(MAJOR_TICKERS)} tickers...")
    
    all_articles = []
    
    # Parallel processing with ThreadPoolExecutor
    with ThreadPoolExecutor(max_workers=10) as executor:
        future_to_ticker = {executor.submit(fetch_news_for_ticker, ticker): ticker for ticker in MAJOR_TICKERS}

        completed = 0
        last_reported = 0
        for future in as_completed(future_to_ticker):
            articles = future.result()
            all_articles.extend(articles)
            completed += 1

            # Update progress every 5 tickers or at completion to reduce spam
            if completed - last_reported >= 5 or completed == len(MAJOR_TICKERS):
                progress_text = f"  Progress: {completed}/{len(MAJOR_TICKERS)} tickers processed"
                print(f"\r{progress_text:<50}", end="", flush=True)
                last_reported = completed

    # Move to next line after completion
    print()  # New line

    # Remove duplicates efficiently
    unique_articles = []
    seen_headlines = set()
    
    for article in all_articles:
        headline_key = article['headline'].lower().strip()
        if headline_key not in seen_headlines:
            seen_headlines.add(headline_key)
            unique_articles.append(article)
    
    print(f"\n📰 Total unique articles: {len(unique_articles)}")
    print(f"📊 Tickers with news: {len(set(article['ticker'] for article in unique_articles))}")
    
    return unique_articles if unique_articles else get_sample_data()

def get_sample_data():
    """Fallback sample data if no news is available"""
    print("⚠️ No news from yfinance. Using sample data...")
    current_date = datetime.now().strftime('%Y-%m-%d')
    return [
        {'headline': 'Apple Inc. reports strong quarterly earnings', 'text': 'Apple beats expectations', 'date': current_date, 'source': 'Sample', 'ticker': 'AAPL'},
        {'headline': 'Microsoft announces AI initiatives', 'text': 'Microsoft expands AI', 'date': current_date, 'source': 'Sample', 'ticker': 'MSFT'},
        {'headline': 'Tesla delivery numbers exceed forecasts', 'text': 'Tesla strong deliveries', 'date': current_date, 'source': 'Sample', 'ticker': 'TSLA'},
        {'headline': 'Amazon Web Services growth continues', 'text': 'AWS shows growth', 'date': current_date, 'source': 'Sample', 'ticker': 'AMZN'},
        {'headline': 'NVIDIA benefits from AI demand', 'text': 'NVIDIA AI surge', 'date': current_date, 'source': 'Sample', 'ticker': 'NVDA'}
    ]

def fetch_government_rss_feed(feed_info):
    """Fetch news from a single government RSS feed"""
    try:
        feed_url = feed_info['url']
        feed_name = feed_info['name']
        category = feed_info['category']
        impact_weight = feed_info['impact_weight']

        # Parse RSS feed
        feed = feedparser.parse(feed_url)

        if not feed.entries:
            return []

        articles = []
        cdt = pytz.timezone('America/Chicago')

        for entry in feed.entries[:5]:  # Top 5 articles per feed
            try:
                headline = entry.get('title', '').strip()
                summary = entry.get('summary', '') or entry.get('description', '')
                link = entry.get('link', '')

                # Parse publication date
                pub_date = entry.get('published_parsed') or entry.get('updated_parsed')
                if pub_date:
                    # Convert from time.struct_time to datetime
                    dt_obj = datetime(*pub_date[:6])
                    # Assume UTC if no timezone info
                    utc = pytz.UTC
                    dt_utc = utc.localize(dt_obj)
                    dt_cdt = dt_utc.astimezone(cdt)

                    formatted_date = dt_cdt.strftime('%Y-%m-%d')
                    formatted_datetime = dt_cdt.strftime('%Y-%m-%d %H:%M:%S CDT')
                    time_ago = get_time_ago(dt_utc)
                else:
                    now = datetime.now(cdt)
                    formatted_date = now.strftime('%Y-%m-%d')
                    formatted_datetime = now.strftime('%Y-%m-%d %H:%M:%S CDT')
                    time_ago = "Unknown"

                if headline and len(headline) > 10:
                    articles.append({
                        'headline': headline,
                        'text': summary or headline,
                        'date': formatted_date,
                        'datetime': formatted_datetime,
                        'time_ago': time_ago,
                        'source': feed_name,
                        'url': link,
                        'category': category,
                        'impact_weight': impact_weight,
                        'ticker': 'POLICY'  # Special ticker for policy news
                    })

            except Exception:
                continue  # Skip problematic entries

        return articles

    except Exception as e:
        print(f"  Error fetching {feed_info['name']}: {e}")
        return []

def fetch_government_news_parallel():
    """Fetch government policy news using parallel processing"""
    print("Fetching government policy news...")
    print(f"Processing {len(GOVERNMENT_RSS_FEEDS)} government sources...")

    all_articles = []

    # Parallel processing with ThreadPoolExecutor
    with ThreadPoolExecutor(max_workers=5) as executor:
        future_to_feed = {executor.submit(fetch_government_rss_feed, feed_info): feed_name
                         for feed_name, feed_info in GOVERNMENT_RSS_FEEDS.items()}

        completed = 0
        for future in as_completed(future_to_feed):
            articles = future.result()
            all_articles.extend(articles)
            completed += 1

            progress_text = f"  Progress: {completed}/{len(GOVERNMENT_RSS_FEEDS)} sources processed"
            print(f"\r{progress_text:<50}", end="", flush=True)

    print()  # New line

    # Remove duplicates
    unique_articles = []
    seen_headlines = set()

    for article in all_articles:
        headline_key = article['headline'].lower().strip()
        if headline_key not in seen_headlines:
            seen_headlines.add(headline_key)
            unique_articles.append(article)

    print(f"🏛️ Government articles: {len(unique_articles)}")
    print(f"📊 Sources with news: {len(set(article['source'] for article in unique_articles))}")

    return unique_articles

def classify_policy_impact(article):
    """Classify the potential market impact of a policy article"""
    text = f"{article['headline']} {article.get('text', '')}".lower()

    # Check for high impact keywords
    high_impact_score = sum(1 for keyword in POLICY_KEYWORDS['high_impact'] if keyword in text)
    medium_impact_score = sum(1 for keyword in POLICY_KEYWORDS['medium_impact'] if keyword in text)
    sector_impact_score = sum(1 for keyword in POLICY_KEYWORDS['sector_specific'] if keyword in text)

    # Calculate impact score
    impact_score = (high_impact_score * 3 + medium_impact_score * 2 + sector_impact_score * 1) * article.get('impact_weight', 0.5)

    # Classify impact level
    if impact_score >= 3:
        impact_level = 'High'
    elif impact_score >= 1.5:
        impact_level = 'Medium'
    elif impact_score >= 0.5:
        impact_level = 'Low'
    else:
        impact_level = 'Minimal'

    return {
        'impact_level': impact_level,
        'impact_score': impact_score,
        'high_impact_keywords': high_impact_score,
        'medium_impact_keywords': medium_impact_score,
        'sector_impact_keywords': sector_impact_score
    }

def analyze_policy_sentiment(government_articles):
    """Analyze sentiment of government policy news with market impact weighting"""
    if not government_articles:
        return {
            'policy_sentiment': 0,
            'policy_mood': 'No Policy Data',
            'high_impact_articles': [],
            'policy_categories': {},
            'total_policy_articles': 0
        }

    policy_scores = []
    policy_details = []
    high_impact_articles = []
    category_sentiment = {}

    for article in government_articles:
        try:
            # Get basic sentiment
            text = f"{article['headline']} {article.get('text', '')}"
            blob = TextBlob(text)
            base_polarity = blob.sentiment.polarity

            # Get policy impact classification
            impact_info = classify_policy_impact(article)

            # Weight sentiment by impact score
            weighted_polarity = base_polarity * (1 + impact_info['impact_score'] * 0.5)

            policy_scores.append(weighted_polarity)

            article_detail = {
                'headline': article['headline'],
                'polarity': base_polarity,
                'weighted_polarity': weighted_polarity,
                'impact_level': impact_info['impact_level'],
                'impact_score': impact_info['impact_score'],
                'category': article.get('category', 'unknown'),
                'source': article.get('source', ''),
                'time_ago': article.get('time_ago', ''),
                'url': article.get('url', '')
            }

            policy_details.append(article_detail)

            # Track high impact articles
            if impact_info['impact_level'] in ['High', 'Medium']:
                high_impact_articles.append(article_detail)

            # Track sentiment by category
            category = article.get('category', 'unknown')
            if category not in category_sentiment:
                category_sentiment[category] = []
            category_sentiment[category].append(weighted_polarity)

        except Exception:
            policy_scores.append(0)
            policy_details.append({
                'headline': article.get('headline', ''),
                'polarity': 0,
                'weighted_polarity': 0,
                'impact_level': 'Minimal',
                'impact_score': 0,
                'category': article.get('category', 'unknown')
            })

    # Calculate overall policy sentiment
    avg_policy_sentiment = np.mean(policy_scores) if policy_scores else 0

    # Determine policy mood
    if avg_policy_sentiment > 0.15:
        policy_mood = "Market Supportive"
    elif avg_policy_sentiment > 0.05:
        policy_mood = "Mildly Supportive"
    elif avg_policy_sentiment > -0.05:
        policy_mood = "Neutral"
    elif avg_policy_sentiment > -0.15:
        policy_mood = "Cautionary"
    else:
        policy_mood = "Market Negative"

    # Calculate category averages
    category_averages = {}
    for category, scores in category_sentiment.items():
        category_averages[category] = {
            'average_sentiment': np.mean(scores),
            'article_count': len(scores)
        }

    return {
        'policy_sentiment': avg_policy_sentiment,
        'policy_mood': policy_mood,
        'high_impact_articles': sorted(high_impact_articles, key=lambda x: x['impact_score'], reverse=True)[:5],
        'policy_categories': category_averages,
        'total_policy_articles': len(government_articles),
        'policy_details': policy_details
    }

def get_market_data_optimized(days=30):
    """Optimized market data fetching - only store what we need"""
    print("Fetching market indices data...")
    end_date = datetime.now()
    start_date = end_date - timedelta(days=days)
    
    market_data = {}
    
    for ticker, name in MARKET_INDICES.items():
        try:
            data = yf.download(ticker, start=start_date, end=end_date, progress=False, auto_adjust=True)
            if not data.empty:
                price_change = (data['Close'].iloc[-1] / data['Close'].iloc[0] - 1) * 100
                price_change = float(price_change.iloc[0]) if hasattr(price_change, 'iloc') else float(price_change)
                market_data[ticker] = {'name': name, 'price_change': price_change}
        except Exception as e:
            print(f"  Error fetching {ticker}: {e}")
    
    return market_data

def analyze_sentiment_batch(news_data):
    """Optimized sentiment analysis"""
    sentiment_scores = []
    sentiment_details = []
    
    for article in news_data:
        try:
            text = f"{article['headline']} {article.get('text', '')}"
            blob = TextBlob(text)
            polarity = blob.sentiment.polarity
            
            sentiment_scores.append(polarity)
            sentiment_details.append({
                'headline': article['headline'],
                'polarity': polarity,
                'category': "Positive" if polarity > 0.1 else "Negative" if polarity < -0.1 else "Neutral"
            })
        except Exception:
            sentiment_scores.append(0)
            sentiment_details.append({
                'headline': article.get('headline', ''),
                'polarity': 0,
                'category': "Neutral"
            })
    
    return sentiment_scores, sentiment_details

def calculate_market_metrics(sentiment_scores, sentiment_details):
    """Calculate market sentiment metrics efficiently"""
    if not sentiment_scores:
        return {"market_mood": "No Data", "average_sentiment": 0, "positive_percentage": 0, "negative_percentage": 0, "neutral_percentage": 0, "total_articles": 0}
    
    avg_sentiment = np.mean(sentiment_scores)
    categories = [detail['category'] for detail in sentiment_details]
    total = len(categories)
    
    positive_pct = (categories.count('Positive') / total) * 100
    negative_pct = (categories.count('Negative') / total) * 100
    neutral_pct = (categories.count('Neutral') / total) * 100
    
    # Determine market mood
    if avg_sentiment > 0.2:
        market_mood = "Very Positive"
    elif avg_sentiment > 0.05:
        market_mood = "Positive"
    elif avg_sentiment > -0.05:
        market_mood = "Neutral"
    elif avg_sentiment > -0.2:
        market_mood = "Negative"
    else:
        market_mood = "Very Negative"
    
    return {
        'market_mood': market_mood,
        'average_sentiment': avg_sentiment,
        'positive_percentage': positive_pct,
        'negative_percentage': negative_pct,
        'neutral_percentage': neutral_pct,
        'total_articles': total
    }

def analyze_ticker_sentiment_optimized(news_data, sentiment_details):
    """Optimized ticker sentiment analysis"""
    ticker_sentiment = {}

    # Group articles by ticker
    ticker_articles = {}
    for i, article in enumerate(news_data):
        ticker = article.get('ticker')
        if ticker:
            if ticker not in ticker_articles:
                ticker_articles[ticker] = []
            ticker_articles[ticker].append((article, sentiment_details[i]))

    # Calculate metrics for each ticker
    for ticker, articles_with_sentiment in ticker_articles.items():
        scores = [sentiment['polarity'] for _, sentiment in articles_with_sentiment]
        categories = [sentiment['category'] for _, sentiment in articles_with_sentiment]

        total_articles = len(scores)
        positive_count = categories.count('Positive')
        negative_count = categories.count('Negative')
        neutral_count = categories.count('Neutral')

        avg_sentiment = np.mean(scores)
        sentiment_volatility = np.std(scores) if len(scores) > 1 else 0
        sentiment_consistency = 1 / (1 + sentiment_volatility)
        overall_score = avg_sentiment * sentiment_consistency

        # Find best and worst headlines with timestamps
        best_article = max(articles_with_sentiment, key=lambda x: x[1]['polarity'])[0]
        worst_article = min(articles_with_sentiment, key=lambda x: x[1]['polarity'])[0]

        ticker_sentiment[ticker] = {
            'average_sentiment': avg_sentiment,
            'sentiment_volatility': sentiment_volatility,
            'sentiment_consistency': sentiment_consistency,
            'overall_score': overall_score,
            'total_articles': total_articles,
            'positive_count': positive_count,
            'negative_count': negative_count,
            'neutral_count': neutral_count,
            'positive_percentage': (positive_count / total_articles) * 100,
            'negative_percentage': (negative_count / total_articles) * 100,
            'best_headline': best_article['headline'],
            'best_headline_time': best_article.get('time_ago', 'Unknown time'),
            'best_headline_datetime': best_article.get('datetime', 'Unknown'),
            'best_headline_url': best_article.get('url', ''),
            'worst_headline': worst_article['headline'],
            'worst_headline_time': worst_article.get('time_ago', 'Unknown time'),
            'worst_headline_datetime': worst_article.get('datetime', 'Unknown'),
            'worst_headline_url': worst_article.get('url', '')
        }

    return ticker_sentiment

def analyze_sector_sentiment_optimized(ticker_sentiment):
    """Optimized sector sentiment analysis"""
    sector_sentiment = {}

    # Group tickers by sector
    for ticker, data in ticker_sentiment.items():
        sector = get_ticker_sector(ticker)

        if sector not in sector_sentiment:
            sector_sentiment[sector] = {
                'tickers': [],
                'sentiment_scores': [],
                'total_articles': 0,
                'positive_count': 0,
                'negative_count': 0,
                'neutral_count': 0
            }

        sector_data = sector_sentiment[sector]
        sector_data['tickers'].append({
            'ticker': ticker,
            'overall_score': data['overall_score'],
            'average_sentiment': data['average_sentiment']
        })

        # Aggregate counts
        sector_data['total_articles'] += data['total_articles']
        sector_data['positive_count'] += data['positive_count']
        sector_data['negative_count'] += data['negative_count']
        sector_data['neutral_count'] += data['neutral_count']
        sector_data['sentiment_scores'].append(data['average_sentiment'])

    # Calculate sector metrics
    sector_rankings = []
    for sector, data in sector_sentiment.items():
        if data['sentiment_scores']:
            avg_sentiment = np.mean(data['sentiment_scores'])
            data['tickers'].sort(key=lambda x: x['overall_score'], reverse=True)

            # Sector strength = average of top 3 performers
            top_performers = data['tickers'][:3]
            sector_strength = np.mean([t['overall_score'] for t in top_performers])

            total_articles = data['total_articles']
            positive_pct = (data['positive_count'] / total_articles) * 100 if total_articles > 0 else 0

            sector_rankings.append({
                'sector': sector,
                'average_sentiment': avg_sentiment,
                'sector_strength': sector_strength,
                'ticker_count': len(data['tickers']),
                'total_articles': total_articles,
                'positive_percentage': positive_pct,
                'top_ticker': data['tickers'][0]['ticker'] if data['tickers'] else 'N/A',
                'top_ticker_score': data['tickers'][0]['overall_score'] if data['tickers'] else 0
            })

    return sorted(sector_rankings, key=lambda x: x['sector_strength'], reverse=True)

def rank_tickers_optimized(ticker_sentiment):
    """Optimized ticker ranking - single sort operation"""
    ticker_infos = []

    for ticker, data in ticker_sentiment.items():
        ticker_infos.append({
            'ticker': ticker,
            'average_sentiment': data['average_sentiment'],
            'overall_score': data['overall_score'],
            'total_articles': data['total_articles'],
            'positive_percentage': data['positive_percentage'],
            'negative_percentage': data['negative_percentage'],
            'best_headline': data['best_headline'],
            'best_headline_time': data['best_headline_time'],
            'best_headline_datetime': data['best_headline_datetime'],
            'best_headline_url': data['best_headline_url'],
            'worst_headline': data['worst_headline'],
            'worst_headline_time': data['worst_headline_time'],
            'worst_headline_datetime': data['worst_headline_datetime'],
            'worst_headline_url': data['worst_headline_url']
        })

    # Return only the best overall ranking (most important)
    return sorted(ticker_infos, key=lambda x: x['overall_score'], reverse=True)

def analyze_market_health_optimized(market_data, sentiment_analysis, policy_analysis=None):
    """Optimized market health analysis with policy integration"""
    if not market_data:
        return {"recommendation": "INSUFFICIENT DATA", "market_trend": "Unknown"}

    price_changes = [data['price_change'] for data in market_data.values()]
    avg_market_change = np.mean(price_changes)

    # Determine market trend
    if avg_market_change > 2:
        market_trend = "Strong Bullish"
    elif avg_market_change > 0.5:
        market_trend = "Bullish"
    elif avg_market_change > -0.5:
        market_trend = "Sideways"
    elif avg_market_change > -2:
        market_trend = "Bearish"
    else:
        market_trend = "Strong Bearish"

    # Generate recommendation with policy consideration
    sentiment_score = sentiment_analysis.get('average_sentiment', 0)
    policy_score = policy_analysis.get('policy_sentiment', 0) if policy_analysis else 0

    # Combine sentiment and policy (policy has 30% weight)
    combined_sentiment = sentiment_score * 0.7 + policy_score * 0.3

    if combined_sentiment > 0.1 and avg_market_change > 1:
        recommendation = "STRONG BUY"
    elif combined_sentiment > 0.05 and avg_market_change > 0:
        recommendation = "BUY"
    elif combined_sentiment > -0.05 and avg_market_change > -1:
        recommendation = "HOLD"
    elif combined_sentiment > -0.1 and avg_market_change > -2:
        recommendation = "CAUTION"
    else:
        recommendation = "SELL"

    # Add policy influence note
    policy_influence = ""
    if policy_analysis and abs(policy_score) > 0.05:
        if policy_score > 0.1:
            policy_influence = " (Policy Supportive)"
        elif policy_score < -0.1:
            policy_influence = " (Policy Headwinds)"
        else:
            policy_influence = " (Policy Neutral)"

    return {
        "recommendation": recommendation + policy_influence,
        "market_trend": market_trend,
        "average_market_change": avg_market_change,
        "combined_sentiment": combined_sentiment,
        "policy_influence": policy_score
    }

def main():
    """main function"""
    print("🚀 FINANCIAL SENTIMENT ANALYZER WITH POLICY ANALYSIS")
    print("=" * 70)

    # Fetch news data in parallel
    news_data = fetch_market_news_parallel()

    if not news_data:
        print("No news data available. Exiting.")
        return

    # Fetch government policy news
    print("\n" + "=" * 70)
    government_data = fetch_government_news_parallel()

    # Get market data
    market_data = get_market_data_optimized()

    # Analyze sentiment
    print("\nAnalyzing market sentiment...")
    sentiment_scores, sentiment_details = analyze_sentiment_batch(news_data)

    # Calculate market metrics
    sentiment_analysis = calculate_market_metrics(sentiment_scores, sentiment_details)

    # Analyze government policy sentiment
    print("Analyzing government policy impact...")
    policy_analysis = analyze_policy_sentiment(government_data)

    # Analyze ticker sentiment
    ticker_sentiment = analyze_ticker_sentiment_optimized(news_data, sentiment_details)

    # Analyze sector sentiment
    sector_rankings = analyze_sector_sentiment_optimized(ticker_sentiment)

    # Rank tickers
    ticker_rankings = rank_tickers_optimized(ticker_sentiment)

    # Analyze market health with policy integration
    market_health = analyze_market_health_optimized(market_data, sentiment_analysis, policy_analysis)

    # Display results
    print("\n📊 MARKET SENTIMENT ANALYSIS")
    print(f"  Overall Sentiment: {sentiment_analysis['market_mood']} ({sentiment_analysis['average_sentiment']:+.3f})")
    print(f"  Positive: {sentiment_analysis['positive_percentage']:.0f}% | Negative: {sentiment_analysis['negative_percentage']:.0f}% | Neutral: {sentiment_analysis['neutral_percentage']:.0f}%")
    print(f"  Total Articles: {sentiment_analysis['total_articles']}")

    # Display policy analysis
    print("\n🏛️ GOVERNMENT POLICY ANALYSIS")
    print(f"  Policy Sentiment: {policy_analysis['policy_mood']} ({policy_analysis['policy_sentiment']:+.3f})")
    print(f"  Total Policy Articles: {policy_analysis['total_policy_articles']}")

    if policy_analysis['policy_categories']:
        print("  Policy Categories:")
        for category, data in policy_analysis['policy_categories'].items():
            emoji = "🟢" if data['average_sentiment'] > 0.05 else "🟡" if data['average_sentiment'] > -0.05 else "🔴"
            print(f"    {emoji} {category.replace('_', ' ').title()}: {data['average_sentiment']:+.3f} ({data['article_count']} articles)")

    # Show high impact policy news
    if policy_analysis['high_impact_articles']:
        print("\n⚡ HIGH IMPACT POLICY NEWS:")
        print("=" * 80)
        for i, article in enumerate(policy_analysis['high_impact_articles'][:3], 1):
            impact_emoji = "🔥" if article['impact_level'] == 'High' else "⚠️"
            headline_link = create_hyperlink(article.get('url', ''), article['headline'])

            print(f"\n  {i}. {impact_emoji} {article['impact_level']} Impact - Score: {article['impact_score']:.2f}")
            print(f"     Sentiment: {article['polarity']:+.3f} | Weighted: {article['weighted_polarity']:+.3f}")
            print(f"     Source: {article.get('source', 'Unknown')}")
            print(f"     [{article.get('time_ago', 'Unknown time')}]: \"{headline_link}\"")

            if i < len(policy_analysis['high_impact_articles'][:3]):
                print("     " + "-" * 70)

    # Get price data for top sector performers
    print("Getting price data for analysis...")
    top_sector_tickers = [sector['top_ticker'] for sector in sector_rankings[:5]]
    sector_price_changes = get_multiple_ticker_prices(top_sector_tickers)

    print("\n🏭 TOP SECTOR PERFORMANCE:")
    for i, sector in enumerate(sector_rankings[:5], 1):
        emoji = "🟢" if sector['average_sentiment'] > 0.1 else "🟡" if sector['average_sentiment'] > 0 else "🔴"
        top_ticker = sector['top_ticker']
        price_change = sector_price_changes.get(top_ticker, 0.0)
        price_emoji = "📈" if price_change > 0 else "📉" if price_change < 0 else "➡️"

        print(f"  {i}. {emoji} {sector['sector']} - Strength: {sector['sector_strength']:.3f}")
        print(f"     Avg Sentiment: {sector['average_sentiment']:+.3f} | Tickers: {sector['ticker_count']} | Positive: {sector['positive_percentage']:.0f}%")
        print(f"     Top Performer: {top_ticker} (Score: {sector['top_ticker_score']:.3f}) {price_emoji} {price_change:+.2f}%")

    # Get price data and analyst recommendations for top 5 sentiment tickers
    top_sentiment_tickers = [ticker['ticker'] for ticker in ticker_rankings[:5]]
    top_sentiment_prices = get_multiple_ticker_prices(top_sentiment_tickers)
    print("Getting analyst recommendations...")
    top_sentiment_recommendations = get_multiple_analyst_recommendations(top_sentiment_tickers)

    print("\n🏆 TOP 5 BEST SENTIMENT TICKERS:")
    print("=" * 80)

    for i, ticker in enumerate(ticker_rankings[:5], 1):
        sector = get_ticker_sector(ticker['ticker'])
        ticker_symbol = ticker['ticker']
        price_change = top_sentiment_prices.get(ticker_symbol, 0.0)
        price_emoji = "📈" if price_change > 0 else "📉" if price_change < 0 else "➡️"

        # Get analyst recommendation
        rec_data = top_sentiment_recommendations.get(ticker_symbol, {})
        analyst_rec = rec_data.get('recommendation', 'N/A')
        upside = rec_data.get('upside_potential', None)

        # Create recommendation display
        rec_display = f"Analyst: {analyst_rec}"
        if upside is not None:
            upside_emoji = "🎯" if upside > 10 else "📊" if upside > 0 else "⚠️"
            rec_display += f" {upside_emoji} {upside:+.1f}% upside"

        # Create clickable headline
        headline_link = create_hyperlink(ticker['best_headline_url'], ticker['best_headline'])

        print(f"\n  {i}. {ticker_symbol} ({sector}) - Score: {ticker['overall_score']:.3f} {price_emoji} {price_change:+.2f}%")
        print(f"     Sentiment: {ticker['average_sentiment']:+.3f} | Articles: {ticker['total_articles']} | Positive: {ticker['positive_percentage']:.0f}%")
        print(f"     {rec_display}")
        print(f"     Best News [{ticker['best_headline_time']}]: \"{headline_link}\"")
        print(f"     Published: {ticker['best_headline_datetime']}")

        # Add separator line between tickers (except for the last one)
        if i < 5:
            print("     " + "-" * 70)

    # Show negative sentiment tickers if any
    negative_tickers = [t for t in ticker_rankings if t['average_sentiment'] < -0.05]
    if negative_tickers:
        # Get price data and analyst recommendations for negative sentiment tickers
        negative_ticker_symbols = [ticker['ticker'] for ticker in negative_tickers[:3]]
        negative_prices = get_multiple_ticker_prices(negative_ticker_symbols)
        negative_recommendations = get_multiple_analyst_recommendations(negative_ticker_symbols)

        print("\n⚠️ TICKERS TO WATCH (Negative Sentiment):")
        print("=" * 80)

        for i, ticker in enumerate(negative_tickers[:3], 1):
            sector = get_ticker_sector(ticker['ticker'])
            ticker_symbol = ticker['ticker']
            price_change = negative_prices.get(ticker_symbol, 0.0)
            price_emoji = "📈" if price_change > 0 else "📉" if price_change < 0 else "➡️"

            # Get analyst recommendation
            rec_data = negative_recommendations.get(ticker_symbol, {})
            analyst_rec = rec_data.get('recommendation', 'N/A')
            upside = rec_data.get('upside_potential', None)

            # Create recommendation display
            rec_display = f"Analyst: {analyst_rec}"
            if upside is not None:
                upside_emoji = "🎯" if upside > 10 else "📊" if upside > 0 else "⚠️"
                rec_display += f" {upside_emoji} {upside:+.1f}% upside"

            # Create clickable headline
            headline_link = create_hyperlink(ticker['worst_headline_url'], ticker['worst_headline'])

            print(f"\n  {i}. {ticker_symbol} ({sector}) - Score: {ticker['average_sentiment']:+.3f} {price_emoji} {price_change:+.2f}%")
            print(f"     Negative: {ticker['negative_percentage']:.0f}% | Articles: {ticker['total_articles']}")
            print(f"     {rec_display}")
            print(f"     Concerning [{ticker['worst_headline_time']}]: \"{headline_link}\"")
            print(f"     Published: {ticker['worst_headline_datetime']}")

            # Add separator line between tickers (except for the last one)
            if i < len(negative_tickers[:3]):
                print("     " + "-" * 70)
    else:
        print("\n✅ No tickers with significantly negative sentiment found!")

    # Combined analysis summary
    print("\n🎯 COMBINED MARKET & POLICY ANALYSIS:")
    print("=" * 80)
    combined_sentiment = market_health.get('combined_sentiment', 0)
    policy_influence = market_health.get('policy_influence', 0)

    print(f"  Market Sentiment: {sentiment_analysis['average_sentiment']:+.3f}")
    print(f"  Policy Influence: {policy_influence:+.3f}")
    print(f"  Combined Score: {combined_sentiment:+.3f}")

    # Policy impact assessment
    if abs(policy_influence) > 0.1:
        if policy_influence > 0:
            policy_impact = "🟢 Government policies are providing significant market support"
        else:
            policy_impact = "🔴 Government policies are creating market headwinds"
    elif abs(policy_influence) > 0.05:
        if policy_influence > 0:
            policy_impact = "🟡 Government policies are mildly supportive"
        else:
            policy_impact = "🟡 Government policies are creating mild concerns"
    else:
        policy_impact = "⚪ Government policies have neutral market impact"

    print(f"  Policy Assessment: {policy_impact}")

    print("\n🚀 RECOMMENDATION:")
    print(f"  {market_health['recommendation']}")
    print(f"  Market Trend: {market_health['market_trend']} ({market_health['average_market_change']:+.2f}%)")

    print("\n📈 MARKET INDICES PERFORMANCE:")
    for index_ticker, data in market_data.items():
        emoji = "📈" if data['price_change'] > 0 else "📉"
        print(f"  {emoji} {data['name']} ({index_ticker}): {data['price_change']:+.2f}%")

    # Show recent news with timestamps and clickable links
    print("\n🕒 RECENT NEWS TIMELINE:")
    recent_articles = sorted(news_data, key=lambda x: x.get('datetime', ''), reverse=True)[:5]
    for i, article in enumerate(recent_articles, 1):
        time_info = article.get('time_ago', 'Unknown time')
        ticker = article.get('ticker', 'N/A')
        headline = article['headline'][:80] + "..." if len(article['headline']) > 80 else article['headline']
        headline_link = create_hyperlink(article.get('url', ''), headline)

        print(f"  {i}. [{time_info}] {ticker}: \"{headline_link}\"")
        print(f"     Published: {article.get('datetime', 'Unknown')}")

if __name__ == "__main__":
    main()